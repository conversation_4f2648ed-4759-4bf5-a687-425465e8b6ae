No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,認証システム,ログイン,管理者ログイン,管理者が正常にログインできること,"1. ログインページ(/login)にアクセス
2. 管理者の認証情報を入力
3. ログインボタンをクリック
4. ダッシュボードページにリダイレクトされることを確認",管理者権限でのアクセス,
2,認証システム,ログイン,カウンセラーログイン,カウンセラーが正常にログインできること,"1. ログインページ(/login)にアクセス
2. カウンセラーの認証情報を入力
3. ログインボタンをクリック
4. アプリケーションページにリダイレクトされることを確認",一般カウンセラー権限,
3,認証システム,ログイン,無効な認証情報,無効な認証情報でログインが拒否されること,"1. ログインページ(/login)にアクセス
2. 無効な認証情報を入力
3. ログインボタンをクリック
4. エラーメッセージが表示されることを確認",認証エラーの適切な表示,
4,認証システム,パスワードリセット,パスワード忘れ,パスワードリセット機能が正常に動作すること,"1. パスワード忘れページ(/forgot-password)にアクセス
2. 登録済みメールアドレスを入力
3. リセットメールが送信されることを確認
4. リセットリンクからパスワード設定ページにアクセス",メール送信機能の確認,
5,認証システム,パスワード設定,新規パスワード設定,新規パスワードが正常に設定できること,"1. パスワード設定ページ(/set-password)にアクセス
2. 新しいパスワードを入力
3. パスワード確認を入力
4. 設定ボタンをクリック
5. 設定完了メッセージが表示されることを確認",パスワード強度の検証,
6,管理機能,カスタマーアカウント管理,アカウント一覧表示,カスタマーアカウント一覧が正常に表示されること,"1. 管理者でログイン
2. 管理ページ(/admin)にアクセス
3. カスタマーアカウント一覧が表示されることを確認
4. ページネーション機能が動作することを確認",管理者権限必須,
7,管理機能,カスタマーアカウント管理,アカウント作成,新規カスタマーアカウントが作成できること,"1. 管理ページでアカウント作成ボタンをクリック
2. 必須項目を入力（顧客名、スラッグ、契約期間等）
3. 利用可能機能を選択
4. 保存ボタンをクリック
5. アカウントが作成されることを確認",機能選択の動作確認,
8,管理機能,カスタマーアカウント管理,アカウント編集,既存カスタマーアカウントが編集できること,"1. アカウント一覧から編集対象を選択
2. 編集モーダルが開くことを確認
3. 情報を変更
4. 保存ボタンをクリック
5. 変更が反映されることを確認",編集権限の確認,
9,管理機能,カスタマーアカウント管理,機能設定,カスタマーの利用可能機能が設定できること,"1. アカウント編集画面で機能設定セクションにアクセス
2. チャットボット、アンケート、ウィザード等の機能を選択
3. 各機能の詳細設定を行う
4. 保存して設定が反映されることを確認",機能別設定の動作,
10,管理機能,カウンセラーアカウント管理,カウンセラー一覧,カウンセラーアカウント一覧が表示されること,"1. カウンセラーアカウントページ(/admin/counserlor-account)にアクセス
2. カウンセラー一覧が表示されることを確認
3. 検索・フィルタ機能が動作することを確認",権限別表示の確認,
11,管理機能,カウンセラーアカウント管理,カウンセラー作成,新規カウンセラーアカウントが作成できること,"1. カウンセラー作成ボタンをクリック
2. 必須情報を入力（名前、メール、権限等）
3. 担当顧客を割り当て
4. 保存ボタンをクリック
5. アカウントが作成されることを確認",権限設定の確認,
12,管理機能,権限管理,権限マトリックス,権限マトリックスが正常に表示されること,"1. 権限管理ページ(/admin/permission)にアクセス
2. 権限マトリックステーブルが表示されることを確認
3. 各役割の権限設定が確認できることを確認",権限の可視化,
13,チャット機能,チャット一覧,ケース一覧表示,対応中のケース一覧が表示されること,"1. アプリケーションページ(/app)にアクセス
2. チャットページ(/app/chats)に移動
3. 対応中のケース一覧が表示されることを確認
4. ケースの状態別フィルタが動作することを確認",ケース状態の確認,
14,チャット機能,チャット一覧,ケース検索,ケース検索機能が正常に動作すること,"1. チャット一覧ページで検索フォームを使用
2. ケースID、相談者名等で検索
3. 検索結果が正しく表示されることを確認
4. 検索条件のクリア機能を確認",検索精度の確認,
15,チャット機能,メッセージ送受信,メッセージ送信,カウンセラーからのメッセージ送信が正常に動作すること,"1. 対象ケースを選択
2. メッセージ入力欄にテキストを入力
3. 送信ボタンをクリック
4. メッセージが送信されることを確認
5. 相談者側に届くことを確認",リアルタイム通信の確認,
16,チャット機能,メッセージ送受信,メッセージ受信,相談者からのメッセージ受信が正常に動作すること,"1. 相談者側からメッセージを送信
2. カウンセラー側でメッセージを受信
3. 未読通知が表示されることを確認
4. メッセージ内容が正しく表示されることを確認",通知機能の確認,
17,チャット機能,ケース管理,ケース状態変更,ケースの状態変更が正常に動作すること,"1. 対象ケースを選択
2. ケース状態変更ボタンをクリック
3. 新しい状態を選択（対応中→待機中等）
4. 状態が変更されることを確認
5. 履歴に記録されることを確認",状態遷移の確認,
18,チャット機能,ケース管理,ケース担当者変更,ケースの担当者変更が正常に動作すること,"1. 対象ケースを選択
2. 担当者変更機能にアクセス
3. 新しい担当者を選択
4. 変更が反映されることを確認
5. 通知が送信されることを確認",権限チェック,
19,チャット機能,ケース管理,メモ機能,ケースにメモを追加できること,"1. 対象ケースを選択
2. メモ入力欄にテキストを入力
3. メモ保存ボタンをクリック
4. メモが保存されることを確認
5. 他のカウンセラーからも閲覧できることを確認",共有メモの確認,
20,チャット機能,ケース管理,タグ機能,ケースにタグを設定できること,"1. 対象ケースを選択
2. タグ設定機能にアクセス
3. 既存タグを選択または新規タグを作成
4. タグが設定されることを確認
5. タグでの検索・フィルタが動作することを確認",タグ管理機能,
21,アンケート機能,アンケート作成,フォーム作成,アンケートフォームが作成できること,"1. アンケートページ(/app/surveys)にアクセス
2. 新規作成ボタンをクリック
3. フォームビルダーで質問項目を追加
4. 質問タイプを選択（テキスト、選択肢等）
5. 保存してアンケートが作成されることを確認",フォームビルダーの動作,
22,アンケート機能,アンケート作成,質問項目設定,様々な質問タイプが設定できること,"1. アンケート作成画面で質問を追加
2. テキスト入力、単一選択、複数選択等のタイプを設定
3. 必須項目の設定
4. 条件分岐の設定
5. プレビュー機能で確認",質問タイプの網羅性,
23,アンケート機能,アンケート管理,一覧表示,作成済みアンケート一覧が表示されること,"1. アンケート一覧ページにアクセス
2. 作成済みアンケートが一覧表示されることを確認
3. ステータス（下書き、公開中等）が表示されることを確認
4. 検索・フィルタ機能が動作することを確認",ステータス管理,
24,アンケート機能,アンケート管理,公開設定,アンケートの公開・非公開が設定できること,"1. アンケート一覧から対象を選択
2. 公開設定を変更
3. 公開URLが生成されることを確認
4. 非公開設定でアクセスできなくなることを確認",公開制御の確認,
25,アンケート機能,回答収集,回答送信,相談者がアンケートに回答できること,"1. 公開されたアンケートURLにアクセス
2. 各質問項目に回答を入力
3. 送信ボタンをクリック
4. 回答が正常に送信されることを確認
5. 完了画面が表示されることを確認",回答データの保存,
26,アンケート機能,回答収集,回答結果表示,アンケート回答結果が表示されること,"1. アンケート管理画面で結果表示を選択
2. 回答一覧が表示されることを確認
3. 統計情報が表示されることを確認
4. 個別回答の詳細が確認できることを確認",データ集計の確認,
27,ウィザード機能,ウィザード作成,フロー作成,ウィザードフローが作成できること,"1. ウィザードページ(/app/wizards)にアクセス
2. 新規作成ボタンをクリック
3. ステップを追加してフローを構築
4. 各ステップの設定を行う
5. 保存してウィザードが作成されることを確認",フロー設計の確認,
28,ウィザード機能,ウィザード作成,条件分岐,ウィザードに条件分岐が設定できること,"1. ウィザード作成画面で分岐ステップを追加
2. 分岐条件を設定
3. 条件に応じた次のステップを設定
4. プレビュー機能で分岐動作を確認",分岐ロジックの確認,
29,ウィザード機能,ウィザード実行,ステップ実行,ウィザードが正常に実行されること,"1. 公開されたウィザードにアクセス
2. 各ステップを順次実行
3. 入力内容に応じて次のステップに進むことを確認
4. 最終ステップまで完了できることを確認",実行フローの確認,
30,ウィザード機能,結果管理,結果表示,ウィザード実行結果が表示されること,"1. ウィザード管理画面で結果表示を選択
2. 実行結果一覧が表示されることを確認
3. 個別結果の詳細が確認できることを確認
4. 結果データのエクスポートが可能なことを確認",結果データの管理,
31,チャットボット機能,ボット設定,基本設定,チャットボットの基本設定ができること,"1. チャットボットページ(/app/chatbot)にアクセス
2. 基本設定タブで名前、ウェルカムメッセージを設定
3. 自動応答の有効化設定
4. 応答待機時間の設定
5. 設定が保存されることを確認",ボット動作の基本設定,
32,チャットボット機能,シナリオ管理,シナリオ作成,チャットボットシナリオが作成できること,"1. シナリオ管理ページ(/app/chatbot/scenario)にアクセス
2. 新規シナリオ作成ボタンをクリック
3. シナリオ名、トリガーキーワードを設定
4. 応答メッセージを設定
5. シナリオが保存されることを確認",シナリオ設計機能,
33,チャットボット機能,シナリオ管理,シナリオ実行,設定したシナリオが正常に実行されること,"1. チャットボットが有効な状態で相談者としてアクセス
2. トリガーキーワードを含むメッセージを送信
3. 設定したシナリオが実行されることを確認
4. 適切な応答メッセージが返されることを確認",自動応答の確認,
34,チャットボット機能,ログ管理,ログ表示,チャットボットの動作ログが表示されること,"1. ログ管理ページ(/app/chatbot/logs)にアクセス
2. チャットボットの動作ログが一覧表示されることを確認
3. フィルタ機能（期間、レベル等）が動作することを確認
4. ログ詳細が確認できることを確認",ログ記録の確認,
35,レポート機能,ケースレポート,レポート生成,ケースレポートが生成できること,"1. レポートページ(/app/reports)にアクセス
2. ケースレポートを選択
3. 期間、条件を設定
4. レポート生成ボタンをクリック
5. レポートが生成されることを確認",データ集計の確認,
36,レポート機能,相談ログ,ログエクスポート,相談ログがエクスポートできること,"1. 相談ログレポートを選択
2. エクスポート条件を設定
3. エクスポート形式を選択（CSV、Excel等）
4. エクスポートボタンをクリック
5. ファイルがダウンロードされることを確認",データエクスポート機能,
37,レポート機能,統計情報,サマリー表示,統計サマリーが表示されること,"1. サマリーレポートページにアクセス
2. 期間別の統計情報が表示されることを確認
3. グラフ・チャートが正常に表示されることを確認
4. データの更新が反映されることを確認",統計データの可視化,
38,設定機能,システム設定,基本設定,システムの基本設定ができること,"1. 設定ページ(/app/settings)にアクセス
2. システム名、ロゴ等の基本情報を設定
3. タイムゾーン、言語設定を変更
4. 設定が保存されることを確認",システム設定の反映,
39,設定機能,通知設定,通知設定,通知設定が変更できること,"1. 通知設定タブにアクセス
2. メール通知、プッシュ通知の設定を変更
3. 通知タイミングの設定
4. 設定が保存されることを確認
5. 実際に通知が送信されることを確認",通知機能の確認,
40,外部連携,LINE連携,LINE設定,LINE連携が設定できること,"1. LINE設定画面にアクセス
2. チャンネルID、シークレット等を設定
3. Webhook URLを設定
4. 接続テストが成功することを確認",LINE API連携,
41,外部連携,LINE連携,メッセージ送受信,LINEでのメッセージ送受信が動作すること,"1. LINE設定完了後、LINEアプリからメッセージを送信
2. システム側でメッセージを受信することを確認
3. システムからLINEへメッセージを送信
4. LINEアプリでメッセージを受信することを確認",リアルタイム連携,
42,外部連携,LIFF連携,LIFF設定,LIFF連携が設定できること,"1. LIFF設定画面にアクセス
2. LIFF IDを設定
3. エンドポイントURLを設定
4. LIFF アプリが正常に動作することを確認",LIFF アプリ連携,
43,外部連携,LIFF連携,LIFF チャット,LIFF経由でのチャットが動作すること,"1. LIFFアプリ(/liff-app)にアクセス
2. チャット画面が表示されることを確認
3. メッセージの送受信が正常に動作することを確認
4. ファイル送信機能が動作することを確認",LIFF チャット機能,
44,ポータル機能,ポータルアクセス,ポータル表示,ポータルページが正常に表示されること,"1. ポータルページ(/portal/[slug])にアクセス
2. 顧客専用ポータルが表示されることを確認
3. ウィザード結果一覧が表示されることを確認
4. 検索・フィルタ機能が動作することを確認",顧客別ポータル,
45,ポータル機能,結果表示,ウィザード結果,ウィザード結果が正常に表示されること,"1. ポータルページでウィザード結果を選択
2. 結果詳細が表示されることを確認
3. 結果データが正しく表示されることを確認
4. エクスポート機能が動作することを確認",結果データの表示,
46,Webアプリ機能,Webアプリアクセス,アクセス制御,Webアプリへのアクセス制御が動作すること,"1. Webアプリページ(/web-app/[slug])にアクセス
2. アクセスコードの入力が求められることを確認
3. 正しいアクセスコードで認証されることを確認
4. 無効なアクセスコードで拒否されることを確認",アクセス制御機能,
47,Webアプリ機能,Webチャット,チャット機能,Webアプリでのチャット機能が動作すること,"1. Webアプリ認証後、チャット画面にアクセス
2. メッセージの送信が正常に動作することを確認
3. カウンセラーからの返信を受信することを確認
4. セッション管理が正常に動作することを確認",Webチャット機能,
48,セキュリティ,認証・認可,権限チェック,適切な権限チェックが動作すること,"1. 各権限レベルのユーザーでログイン
2. アクセス可能なページが権限に応じて制限されることを確認
3. 権限のないページにアクセスした際にエラーが表示されることを確認",権限ベースアクセス制御,
49,セキュリティ,データ保護,データ暗号化,機密データが適切に保護されていること,"1. ネットワーク通信がHTTPS化されていることを確認
2. パスワードがハッシュ化されていることを確認
3. 個人情報が適切にマスキングされていることを確認",データ保護機能,
50,セキュリティ,セッション管理,セッション制御,セッション管理が適切に動作すること,"1. ログイン後のセッション有効期限を確認
2. 非アクティブ時の自動ログアウトを確認
3. 複数デバイスでのログイン制御を確認
4. ログアウト時のセッション無効化を確認",セッション制御機能,
51,パフォーマンス,ページ読み込み,初期表示,各ページの初期表示が適切な時間で完了すること,"1. 各主要ページにアクセス
2. ページ読み込み時間を測定
3. 3秒以内に表示されることを確認
4. リソースの読み込み状況を確認",パフォーマンス要件,
52,パフォーマンス,データ処理,大量データ,大量データの処理が適切に動作すること,"1. 大量のケースデータを作成
2. 一覧表示の動作を確認
3. 検索・フィルタ機能の動作を確認
4. ページネーションの動作を確認",スケーラビリティ,
53,パフォーマンス,リアルタイム通信,同時接続,複数ユーザーの同時接続が正常に動作すること,"1. 複数のカウンセラーが同時にログイン
2. 同時にチャット機能を使用
3. メッセージの送受信が正常に動作することを確認
4. システムの応答性を確認",同時接続性能,
54,ユーザビリティ,画面操作,レスポンシブ対応,レスポンシブデザインが適切に動作すること,"1. デスクトップ、タブレット、スマートフォンでアクセス
2. 各デバイスで画面が適切に表示されることを確認
3. 操作性が保たれることを確認
4. 機能が正常に動作することを確認",マルチデバイス対応,
55,ユーザビリティ,画面操作,ナビゲーション,ナビゲーションが直感的に操作できること,"1. メインナビゲーションの動作を確認
2. サブナビゲーションの動作を確認
3. パンくずナビゲーションの表示を確認
4. 戻る・進むボタンの動作を確認",ナビゲーション設計,
56,ユーザビリティ,エラーハンドリング,エラー表示,エラーメッセージが適切に表示されること,"1. 意図的にエラーを発生させる
2. ユーザーフレンドリーなエラーメッセージが表示されることを確認
3. エラーからの復旧方法が示されることを確認
4. システムエラーページが適切に表示されることを確認",エラーUX,
57,国際化,多言語対応,言語切り替え,多言語対応が正常に動作すること,"1. 言語設定を日本語に設定
2. 画面表示が日本語になることを確認
3. 英語、ポルトガル語に切り替え
4. 各言語で適切に表示されることを確認",i18n機能,
58,国際化,多言語対応,文字エンコーディング,多言語文字が正しく表示されること,"1. 日本語、英語、ポルトガル語の文字を入力
2. 文字化けが発生しないことを確認
3. データベースに正しく保存されることを確認
4. 表示時に正しく復元されることを確認",文字エンコーディング,
59,アクセシビリティ,キーボード操作,キーボードナビゲーション,キーボードのみで操作できること,"1. マウスを使用せずにキーボードのみで操作
2. Tabキーでフォーカス移動ができることを確認
3. Enterキーでボタンが押せることを確認
4. 全ての機能がキーボードで操作できることを確認",アクセシビリティ対応,
60,アクセシビリティ,スクリーンリーダー,読み上げ対応,スクリーンリーダーで適切に読み上げられること,"1. スクリーンリーダーを有効にして操作
2. 画面内容が適切に読み上げられることを確認
3. フォーム項目のラベルが読み上げられることを確認
4. ボタンの機能が理解できることを確認",スクリーンリーダー対応,
61,データ整合性,データ同期,リアルタイム同期,データのリアルタイム同期が正常に動作すること,"1. 複数のブラウザで同じケースを開く
2. 一方でメッセージを送信
3. 他方で即座に反映されることを確認
4. データの整合性が保たれることを確認",データ同期機能,
62,データ整合性,データバックアップ,データ保護,データのバックアップ・復旧が正常に動作すること,"1. システムデータのバックアップを実行
2. バックアップファイルが作成されることを確認
3. データ復旧テストを実行
4. データが正しく復旧されることを確認",データ保護機能,
63,統合テスト,エンドツーエンド,相談フロー全体,相談の開始から終了までの全フローが正常に動作すること,"1. 相談者がWebアプリまたはLINEからアクセス
2. 初回メッセージを送信
3. カウンセラーが対応開始
4. チャットでのやり取り
5. ケース完了まで一連の流れを確認",E2Eテスト,
64,統合テスト,エンドツーエンド,アンケート実施フロー,アンケートの作成から回答収集までの全フローが正常に動作すること,"1. 管理者がアンケートを作成
2. 公開設定を行う
3. 相談者がアンケートに回答
4. 回答結果の集計・表示
5. レポート出力まで一連の流れを確認",アンケートE2E,
65,統合テスト,エンドツーエンド,ウィザード実行フロー,ウィザードの作成から実行結果確認までの全フローが正常に動作すること,"1. 管理者がウィザードを作成
2. 公開設定を行う
3. 相談者がウィザードを実行
4. 実行結果の保存・表示
5. ポータルでの結果確認まで一連の流れを確認",ウィザードE2E,
66,統合テスト,エンドツーエンド,チャットボット対応フロー,チャットボットの設定から自動対応までの全フローが正常に動作すること,"1. 管理者がチャットボットを設定
2. シナリオを作成・公開
3. 相談者がトリガーキーワードを送信
4. チャットボットが自動応答
5. 必要に応じてカウンセラーに引き継ぎ",チャットボットE2E,
67,統合テスト,システム連携,外部システム連携,外部システムとの連携が正常に動作すること,"1. LINE API連携の動作確認
2. メール送信機能の動作確認
3. 外部認証システムとの連携確認
4. データエクスポート機能の動作確認",外部連携テスト,
68,統合テスト,負荷テスト,高負荷状況,高負荷状況下でのシステム動作を確認すること,"1. 大量のユーザーが同時アクセス
2. 大量のメッセージ送受信
3. システムの応答性を確認
4. エラー発生率を確認
5. リソース使用量を監視",負荷テスト,
69,統合テスト,障害テスト,障害復旧,システム障害からの復旧が正常に動作すること,"1. 意図的にシステム障害を発生
2. 障害検知機能の動作確認
3. 自動復旧機能の動作確認
4. データ整合性の確認
5. ユーザーへの影響を最小化",障害復旧テスト,
70,統合テスト,セキュリティテスト,脆弱性テスト,セキュリティ脆弱性がないことを確認すること,"1. SQLインジェクション攻撃テスト
2. XSS攻撃テスト
3. CSRF攻撃テスト
4. 認証バイパステスト
5. データ漏洩テスト",セキュリティテスト,
