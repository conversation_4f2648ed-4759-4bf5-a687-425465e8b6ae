<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="新規チャットボット作成">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div class="p-6">
      <div class="max-w-4xl mx-auto">
        <form @submit.prevent="createChatbot" class="space-y-8">
          <!-- Basic Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-blue-500" />
                <h3 class="text-lg font-semibold">基本情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボット名" required>
                <UInput 
                  v-model="form.name" 
                  placeholder="例: サポートボット"
                  :error="errors.name"
                />
              </UFormGroup>
              <UFormGroup label="説明">
                <UTextarea 
                  v-model="form.description"
                  placeholder="このチャットボットの用途や機能について説明してください"
                  :rows="3"
                />
              </UFormGroup>
              <UFormGroup label="ウェルカムメッセージ" required>
                <UTextarea 
                  v-model="form.welcomeMessage"
                  placeholder="こんにちは！何かお困りのことはありませんか？"
                  :rows="4"
                  :error="errors.welcomeMessage"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    ユーザーが最初にチャットボットと接触した際に表示されるメッセージです
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Settings -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-cog-6-tooth" class="text-green-500" />
                <h3 class="text-lg font-semibold">動作設定</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボットの有効化">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    作成後すぐにチャットボットを有効にします
                  </span>
                  <UToggle
                    v-model="form.isActive"
                    on-icon="i-heroicons-check-20-solid"
                    off-icon="i-heroicons-x-mark-20-solid"
                  />
                </div>
              </UFormGroup>
              
              <UFormGroup label="自動応答機能">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    一定時間後に自動でメッセージを送信します
                  </span>
                  <UToggle
                    v-model="form.autoResponse"
                    on-icon="i-heroicons-check-20-solid"
                    off-icon="i-heroicons-x-mark-20-solid"
                  />
                </div>
              </UFormGroup>
              
              <UFormGroup 
                v-if="form.autoResponse"
                label="応答待機時間（秒）" 
                required
              >
                <UInput 
                  v-model="form.responseDelay" 
                  type="number"
                  min="1"
                  max="300"
                  placeholder="30"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    1秒から300秒（5分）まで設定可能です
                  </span>
                </template>
              </UFormGroup>
              
              <UFormGroup label="営業時間外メッセージ">
                <UTextarea 
                  v-model="form.offHoursMessage"
                  placeholder="申し訳ございませんが、現在営業時間外です。営業時間は平日9:00-18:00となっております。"
                  :rows="3"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    営業時間外にユーザーがアクセスした際に表示されるメッセージです
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Initial Scenarios -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-document-text" class="text-purple-500" />
                <h3 class="text-lg font-semibold">初期シナリオ設定</h3>
              </div>
            </template>
            <div class="space-y-4">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                チャットボット作成時に自動で追加するシナリオを選択してください
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  v-for="template in scenarioTemplates" 
                  :key="template.id"
                  class="border rounded-lg p-4 cursor-pointer transition-colors"
                  :class="{
                    'border-blue-500 bg-blue-50 dark:bg-blue-900/20': form.selectedTemplates.includes(template.id),
                    'border-gray-200 dark:border-gray-700 hover:border-gray-300': !form.selectedTemplates.includes(template.id)
                  }"
                  @click="toggleTemplate(template.id)"
                >
                  <div class="flex items-start space-x-3">
                    <UCheckbox 
                      :model-value="form.selectedTemplates.includes(template.id)"
                      @update:model-value="toggleTemplate(template.id)"
                    />
                    <div class="flex-1">
                      <h4 class="font-medium">{{ template.name }}</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {{ template.description }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </UCard>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3">
            <UButton 
              color="gray" 
              variant="soft" 
              @click="$router.back()"
              :disabled="loading"
            >
              キャンセル
            </UButton>
            <UButton 
              color="primary" 
              type="submit"
              :loading="loading"
            >
              チャットボットを作成
            </UButton>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const router = useRouter()
const loading = ref(false)

const form = reactive({
  name: '',
  description: '',
  welcomeMessage: '',
  isActive: true,
  autoResponse: false,
  responseDelay: 30,
  offHoursMessage: '',
  selectedTemplates: []
})

const errors = reactive({
  name: '',
  welcomeMessage: ''
})

const scenarioTemplates = [
  {
    id: 'template_001',
    name: '基本的な挨拶対応',
    description: 'こんにちは、ありがとうございますなどの基本的な挨拶に対応'
  },
  {
    id: 'template_002',
    name: 'FAQ自動応答',
    description: 'よくある質問に対する自動応答シナリオ'
  },
  {
    id: 'template_003',
    name: '問い合わせ受付',
    description: 'ユーザーからの問い合わせを受け付けて担当者に転送'
  },
  {
    id: 'template_004',
    name: '営業時間案内',
    description: '営業時間や連絡先の案内を行う'
  }
]

const validateForm = () => {
  errors.name = ''
  errors.welcomeMessage = ''
  
  let isValid = true
  
  if (!form.name.trim()) {
    errors.name = 'チャットボット名は必須です'
    isValid = false
  }
  
  if (!form.welcomeMessage.trim()) {
    errors.welcomeMessage = 'ウェルカムメッセージは必須です'
    isValid = false
  }
  
  return isValid
}

const toggleTemplate = (templateId: string) => {
  const index = form.selectedTemplates.indexOf(templateId)
  if (index > -1) {
    form.selectedTemplates.splice(index, 1)
  } else {
    form.selectedTemplates.push(templateId)
  }
}

const createChatbot = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    // TODO: Implement API call to create chatbot
    console.log('Creating chatbot:', form)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const toast = useToast()
    toast.add({
      title: 'チャットボットを作成しました',
      description: `${form.name}が正常に作成されました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
    // Navigate back to chatbot list
    router.push('/app/chatbot')
    
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの作成に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}
</script>
