<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="チャットボット一覧" />
    <div class="px-6 pt-6">
      <BaseTable
        title="チャットボット一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalChatbotsCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="createChatbot"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshChatbots"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedChatbots"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-noto-v1-robot" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                チャットボットがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-noto-v1-robot" class="text-blue-500" />
              <div>
                <ULink
                  @click="viewChatbotDetail(row)"
                  inactive-class="text-primary-500 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-200 cursor-pointer"
                >
                  {{ row.name }}
                </ULink>
                <p class="text-xs text-gray-500">ID: {{ row.id }}</p>
              </div>
            </div>
          </template>
          <template #description-data="{ row }">
            <div class="max-w-xs">
              <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                {{ row.description || '説明なし' }}
              </p>
            </div>
          </template>
          <template #status-data="{ row }">
            <div class="flex flex-col space-y-1">
              <UBadge
                :color="row.isActive ? 'green' : 'gray'"
                variant="soft"
                size="sm"
              >
                {{ row.isActive ? '有効' : '無効' }}
              </UBadge>
              <UBadge
                :color="row.autoResponse ? 'blue' : 'gray'"
                variant="soft"
                size="sm"
              >
                {{ row.autoResponse ? '自動応答' : '手動応答' }}
              </UBadge>
            </div>
          </template>
          <template #stats-data="{ row }">
            <div class="text-sm">
              <div>シナリオ: {{ row.scenarioCount }}個</div>
              <div>テンプレート: {{ row.templateCount }}個</div>
            </div>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-eye"
                size="xs"
                color="blue"
                variant="soft"
                label="詳細"
                @click="viewChatbotDetail(row)"
              />
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="editChatbot(row)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleChatbot(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const loading = ref(false)

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})

// Mock data for chatbots
const chatbots = ref([
  {
    id: 'bot_001',
    name: 'サポートボット',
    description: '一般的なサポート業務を担当するチャットボット',
    welcomeMessage: 'こんにちは！何かお困りのことはありませんか？',
    isActive: true,
    autoResponse: true,
    responseDelay: 30,
    scenarioCount: 5,
    templateCount: 8,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'bot_002',
    name: 'FAQ自動応答ボット',
    description: 'よくある質問に自動で回答するチャットボット',
    welcomeMessage: 'よくある質問にお答えします。どのようなことでお困りですか？',
    isActive: true,
    autoResponse: false,
    responseDelay: 60,
    scenarioCount: 12,
    templateCount: 15,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'bot_003',
    name: '営業時間外対応ボット',
    description: '営業時間外の問い合わせに対応するチャットボット',
    welcomeMessage: '申し訳ございませんが、現在営業時間外です。',
    isActive: false,
    autoResponse: true,
    responseDelay: 10,
    scenarioCount: 3,
    templateCount: 5,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 'bot_004',
    name: '製品案内ボット',
    description: '製品情報の案内を行うチャットボット',
    welcomeMessage: '製品についてご質問がございましたらお聞かせください。',
    isActive: true,
    autoResponse: true,
    responseDelay: 15,
    scenarioCount: 8,
    templateCount: 12,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-22')
  },
  {
    id: 'bot_005',
    name: '技術サポートボット',
    description: '技術的な問題の解決をサポートするチャットボット',
    welcomeMessage: '技術的な問題でお困りですか？詳しくお聞かせください。',
    isActive: false,
    autoResponse: false,
    responseDelay: 45,
    scenarioCount: 15,
    templateCount: 20,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-25')
  }
])

// Table columns
const columns = [
  {
    key: "name",
    label: "チャットボット名",
    sortable: true,
  },
  {
    key: "description",
    label: "説明",
    sortable: false,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "stats",
    label: "統計",
    sortable: false,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalChatbotsCount = computed(() => chatbots.value.length)

const paginatedChatbots = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...chatbots.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalChatbotsCount.value)
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const createChatbot = () => {
  navigateTo('/app/chatbot/create')
}

const viewChatbotDetail = (chatbot: any) => {
  navigateTo(`/app/chatbot/detail/${chatbot.id}`)
}

const editChatbot = (chatbot: any) => {
  navigateTo(`/app/chatbot/edit/${chatbot.id}`)
}

const toggleChatbot = async (chatbot: any) => {
  chatbot.isActive = !chatbot.isActive
  chatbot.updatedAt = new Date()

  const toast = useToast()
  toast.add({
    title: chatbot.isActive ? 'チャットボットを有効化しました' : 'チャットボットを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const refreshChatbots = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'チャットボット一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}
</script>
