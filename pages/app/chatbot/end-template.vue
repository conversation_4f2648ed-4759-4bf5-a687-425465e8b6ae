<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="終了テンプレート" />
    <div class="px-6 pt-6">
      <BaseTable
        title="終了テンプレート管理"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalTemplatesCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="createTemplate"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshTemplates"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedTemplates"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-check" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                終了テンプレートがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-heroicons-document-check" class="text-blue-500" />
              <div>
                <div class="font-semibold">{{ row.name }}</div>
                <p class="text-xs text-gray-500">{{ getCategoryLabel(row.category) }}</p>
              </div>
            </div>
          </template>
          <template #finalQuestion-data="{ row }">
            <div class="max-w-md">
              <div class="bg-gray-50 dark:bg-gray-800 rounded p-2 text-sm">
                <p class="line-clamp-2">{{ row.finalQuestion }}</p>
              </div>
            </div>
          </template>
          <template #choices-data="{ row }">
            <div class="max-w-xs">
              <UBadge variant="soft" size="sm">
                {{ row.choices.length }}個
              </UBadge>
              <div class="mt-1 text-xs text-gray-500">
                <div v-for="(choice, index) in row.choices.slice(0, 2)" :key="choice.id" class="truncate">
                  {{ index + 1 }}. {{ choice.text }}
                </div>
                <div v-if="row.choices.length > 2" class="text-gray-400">
                  他{{ row.choices.length - 2 }}個...
                </div>
              </div>
            </div>
          </template>
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="soft"
              size="sm"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="editTemplate(row)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleTemplate(row)"
              />
              <UButton
                icon="i-heroicons-document-duplicate"
                size="xs"
                color="gray"
                variant="soft"
                label="複製"
                @click="duplicateTemplate(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingTemplate ? 'テンプレート編集' : '新規テンプレート作成' }}
          </h3>
        </template>
        
        <div class="space-y-6">
          <UFormGroup label="終了テンプレート名" required>
            <UInput
              v-model="templateForm.templateName"
              placeholder="例: 問題解決完了テンプレート"
            />
            <template #help>
              <span class="text-xs text-gray-500">
                管理項目用です。ユーザーには表示されません。管理しやすい名前で登録してください。
              </span>
            </template>
          </UFormGroup>

          <UFormGroup label="最終設問" required>
            <UTextarea
              v-model="templateForm.finalQuestion"
              placeholder="チャットボットの最後に表示する設問を入力してください"
              :rows="3"
              :maxlength="100"
            />
            <template #help>
              <div class="flex justify-between text-xs">
                <span class="text-gray-500">
                  チャットボットの最後に表示する設問を、100文字以内で登録します。
                </span>
                <span :class="templateForm.finalQuestion?.length > 100 ? 'text-red-500' : 'text-gray-500'">
                  {{ templateForm.finalQuestion?.length || 0 }}/100
                </span>
              </div>
            </template>
          </UFormGroup>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">選択肢</h4>
              <UButton
                v-if="templateForm.choices.length < 4"
                size="xs"
                variant="soft"
                icon="i-heroicons-plus"
                @click="addChoice"
              >
                選択肢を追加
              </UButton>
            </div>

            <div v-for="(choice, index) in templateForm.choices" :key="choice.id" class="border rounded-lg p-4 space-y-3">
              <div class="flex items-center justify-between">
                <h5 class="text-sm font-medium">選択肢{{ index + 1 }}</h5>
                <UButton
                  v-if="templateForm.choices.length > 1"
                  size="xs"
                  variant="soft"
                  color="red"
                  icon="i-heroicons-trash"
                  @click="removeChoice(index)"
                />
              </div>

              <UFormGroup label="選択肢内容" required>
                <UTextarea
                  v-model="choice.text"
                  placeholder="選択肢として表示する内容を入力してください"
                  :rows="2"
                  :maxlength="150"
                />
                <template #help>
                  <div class="flex justify-between text-xs">
                    <span class="text-gray-500">
                      選択肢として表示する内容を、全角150文字以内で登録します。
                    </span>
                    <span :class="choice.text?.length > 150 ? 'text-red-500' : 'text-gray-500'">
                      {{ choice.text?.length || 0 }}/150
                    </span>
                  </div>
                </template>
              </UFormGroup>

              <UFormGroup label="返答メッセージ（任意）">
                <UTextarea
                  v-model="choice.responseMessage"
                  placeholder="選択肢が選ばれたときに返すメッセージ（任意）"
                  :rows="2"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    選択肢が選ばれたときにメッセージを返したい場合は、返答メッセージも登録します。
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup>
                <UCheckbox
                  v-model="choice.openOneOnOneTalk"
                  label="トークを開設する"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    該当選択肢が選ばれた際に1:1トークを開設させたい場合は、チェックを入れます。
                  </span>
                </template>
              </UFormGroup>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton
              color="primary"
              @click="saveTemplate"
              :loading="saving"
              :disabled="!isFormValid"
            >
              {{ editingTemplate ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { EndTemplate, EndTemplateChoice } from '@/types'

definePageMeta({
  navigationPage: "app-chatbot-end-template",
  middleware: ["permissions"],
});

const showModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const editingTemplate = ref<EndTemplate | null>(null)

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})

// Helper function to generate unique choice IDs
function generateChoiceId(): string {
  return 'choice_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// Form data
const templateForm = reactive({
  templateName: '',
  finalQuestion: '',
  choices: [
    {
      id: generateChoiceId(),
      text: '',
      responseMessage: '',
      openOneOnOneTalk: false
    }
  ] as EndTemplateChoice[]
})

// Choice management functions
const addChoice = () => {
  if (templateForm.choices.length < 4) {
    templateForm.choices.push({
      id: generateChoiceId(),
      text: '',
      responseMessage: '',
      openOneOnOneTalk: false
    })
  }
}

const removeChoice = (index: number) => {
  if (templateForm.choices.length > 1) {
    templateForm.choices.splice(index, 1)
  }
}

// Validation
const isFormValid = computed(() => {
  // Check template name
  if (!templateForm.templateName.trim()) return false

  // Check final question (max 100 chars)
  if (!templateForm.finalQuestion.trim() || templateForm.finalQuestion.length > 100) return false

  // Check choices
  if (templateForm.choices.length === 0) return false

  // Check each choice
  for (const choice of templateForm.choices) {
    if (!choice.text.trim() || choice.text.length > 150) return false
  }

  return true
})

const categoryOptions = [
  { label: '問題解決完了', value: 'resolved' },
  { label: '情報提供完了', value: 'information' },
  { label: '案内完了', value: 'guidance' },
  { label: '営業時間外', value: 'off-hours' },
  { label: 'エラー対応', value: 'error' },
  { label: 'その他', value: 'other' }
]

const templates = ref<EndTemplate[]>([
  {
    id: '1',
    templateName: '問題解決完了テンプレート',
    finalQuestion: 'ご質問は解決されましたでしょうか？',
    choices: [
      {
        id: 'choice_1_1',
        text: '解決しました。ありがとうございました。',
        responseMessage: 'お役に立てて良かったです。また何かございましたらお気軽にお声かけください。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_1_2',
        text: 'まだ解決していません。',
        responseMessage: '申し訳ございません。担当者がご対応いたします。',
        openOneOnOneTalk: true
      },
      {
        id: 'choice_1_3',
        text: '別の質問があります。',
        responseMessage: '',
        openOneOnOneTalk: true
      }
    ],
    isActive: true,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: '2',
    templateName: '情報提供完了テンプレート',
    finalQuestion: '必要な情報をお伝えできましたでしょうか？',
    choices: [
      {
        id: 'choice_2_1',
        text: '十分な情報をいただけました。',
        responseMessage: 'ありがとうございます。今後ともよろしくお願いいたします。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_2_2',
        text: '追加で質問があります。',
        responseMessage: '',
        openOneOnOneTalk: true
      }
    ],
    isActive: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  },
  {
    id: '3',
    templateName: '営業時間外対応テンプレート',
    finalQuestion: '営業時間外のため、お急ぎの場合はどうされますか？',
    choices: [
      {
        id: 'choice_3_1',
        text: '営業時間内に改めて連絡します。',
        responseMessage: '承知いたしました。営業時間は平日9:00-18:00です。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_3_2',
        text: '緊急事態です。',
        responseMessage: '緊急事態の場合は、担当者にお繋ぎいたします。',
        openOneOnOneTalk: true
      }
    ],
    isActive: false,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05'
  }
])

// Table columns
const columns = [
  {
    key: "templateName",
    label: "テンプレート名",
    sortable: true,
  },
  {
    key: "finalQuestion",
    label: "最終設問",
    sortable: false,
  },
  {
    key: "choices",
    label: "選択肢数",
    sortable: false,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalTemplatesCount = computed(() => templates.value.length)

const paginatedTemplates = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...templates.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (!aVal && !bVal) return 0
    if (!aVal) return 1
    if (!bVal) return -1

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalTemplatesCount.value)
})



const formatDateTime = (date: string | Date) => {
  console.log("🚀 ~ formatDateTime ~ date:", date)
  // return empty string if date is not set
  if (!date) return ''

  // Convert string to Date object if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    console.warn('Invalid date:', date)
    return ''
  }

  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

const getCategoryLabel = (category: string) => {
  const option = categoryOptions.find(opt => opt.value === category)
  return option ? option.label : category
}

const refreshTemplates = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'テンプレート一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}

const createTemplate = () => {
  editingTemplate.value = null
  Object.assign(templateForm, {
    templateName: '',
    finalQuestion: '',
    choices: [
      {
        id: generateChoiceId(),
        text: '',
        responseMessage: '',
        openOneOnOneTalk: false
      }
    ]
  })
  showModal.value = true
}

const editTemplate = (template: EndTemplate) => {
  editingTemplate.value = template
  Object.assign(templateForm, {
    templateName: template.templateName,
    finalQuestion: template.finalQuestion,
    choices: template.choices.map(choice => ({ ...choice })) // Deep copy choices
  })
  showModal.value = true
}

const saveTemplate = async () => {
  if (!isFormValid.value) {
    const toast = useToast()
    toast.add({
      title: '入力エラー',
      description: '入力内容を確認してください。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
    return
  }

  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingTemplate.value) {
      // Update existing template
      Object.assign(editingTemplate.value, {
        templateName: templateForm.templateName,
        finalQuestion: templateForm.finalQuestion,
        choices: templateForm.choices.map(choice => ({ ...choice })),
        updatedAt: new Date().toISOString()
      })
    } else {
      // Create new template
      const newTemplate: EndTemplate = {
        id: Date.now().toString(),
        templateName: templateForm.templateName,
        finalQuestion: templateForm.finalQuestion,
        choices: templateForm.choices.map(choice => ({ ...choice })),
        isActive: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      templates.value.push(newTemplate)
    }

    closeModal()

    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `テンプレートを${editingTemplate.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'テンプレートの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingTemplate.value = null
}

const toggleTemplate = async (template: EndTemplate) => {
  template.isActive = !template.isActive
  template.updatedAt = new Date().toISOString()

  const toast = useToast()
  toast.add({
    title: template.isActive ? 'テンプレートを有効化しました' : 'テンプレートを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateTemplate = (template: EndTemplate) => {
  const newTemplate: EndTemplate = {
    ...template,
    id: Date.now().toString(),
    templateName: `${template.templateName} (コピー)`,
    choices: template.choices.map(choice => ({
      ...choice,
      id: generateChoiceId()
    })),
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  templates.value.push(newTemplate)

  const toast = useToast()
  toast.add({
    title: 'テンプレートを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

// const deleteTemplate = (template: EndTemplate) => {
//   const index = templates.value.findIndex(t => t.id === template.id)
//   if (index > -1) {
//     templates.value.splice(index, 1)
//
//     const toast = useToast()
//     toast.add({
//       title: 'テンプレートを削除しました',
//       icon: 'i-heroicons-check-circle',
//       color: 'green'
//     })
//   }
// }
</script>
