<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="終了テンプレート" />
    <div class="px-6 pt-6">
      <BaseTable
        title="終了テンプレート管理"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalTemplatesCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="createTemplate"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshTemplates"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedTemplates"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-check" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                終了テンプレートがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-heroicons-document-check" class="text-blue-500" />
              <div>
                <div class="font-semibold">{{ row.name }}</div>
                <p class="text-xs text-gray-500">{{ getCategoryLabel(row.category) }}</p>
              </div>
            </div>
          </template>
          <template #content-data="{ row }">
            <div class="max-w-md">
              <div class="bg-gray-50 dark:bg-gray-800 rounded p-2 text-sm">
                <p class="line-clamp-3">{{ row.content }}</p>
              </div>
            </div>
          </template>
          <template #condition-data="{ row }">
            <div class="max-w-xs">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ row.condition || '条件なし' }}
              </p>
            </div>
          </template>
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="soft"
              size="sm"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="editTemplate(row)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleTemplate(row)"
              />
              <UButton
                icon="i-heroicons-document-duplicate"
                size="xs"
                color="gray"
                variant="soft"
                label="複製"
                @click="duplicateTemplate(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingTemplate ? 'テンプレート編集' : '新規テンプレート作成' }}
          </h3>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="テンプレート名" required>
            <UInput v-model="templateForm.name" placeholder="例: 問題解決完了" />
          </UFormGroup>
          
          <UFormGroup label="カテゴリ" required>
            <USelect 
              v-model="templateForm.category" 
              :options="categoryOptions"
              placeholder="カテゴリを選択"
            />
          </UFormGroup>
          
          <UFormGroup label="テンプレート内容" required>
            <UTextarea 
              v-model="templateForm.content" 
              placeholder="お疲れ様でした。ご質問は解決されましたでしょうか？&#10;他にもご不明な点がございましたら、いつでもお声かけください。"
              :rows="6"
            />
            <template #help>
              <span class="text-xs text-gray-500">
                改行は自動的に反映されます。変数 {userName} を使用してユーザー名を挿入できます。
              </span>
            </template>
          </UFormGroup>
          
          <UFormGroup label="使用条件">
            <UTextarea 
              v-model="templateForm.condition" 
              placeholder="このテンプレートを使用する条件を記述してください（任意）"
              :rows="2"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton color="primary" @click="saveTemplate" :loading="saving">
              {{ editingTemplate ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-end-template",
  middleware: ["permissions"],
});

const showModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const editingTemplate = ref(null)

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})

const categoryOptions = [
  { label: '問題解決完了', value: 'resolved' },
  { label: '情報提供完了', value: 'information' },
  { label: '案内完了', value: 'guidance' },
  { label: '営業時間外', value: 'off-hours' },
  { label: 'エラー対応', value: 'error' },
  { label: 'その他', value: 'other' }
]

const templates = ref([
  {
    id: 1,
    name: '問題解決完了',
    category: 'resolved',
    content: 'お疲れ様でした。ご質問は解決されましたでしょうか？\n他にもご不明な点がございましたら、いつでもお声かけください。\n\n本日はありがとうございました。',
    condition: '問題が解決された場合',
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 2,
    name: '情報提供完了',
    category: 'information',
    content: '必要な情報をお伝えできましたでしょうか？\n\n追加でご質問がございましたら、お気軽にお声かけください。',
    condition: '情報提供が完了した場合',
    isActive: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 3,
    name: '営業時間外対応',
    category: 'off-hours',
    content: '申し訳ございませんが、現在営業時間外です。\n営業時間は平日9:00-18:00となっております。\n\n営業時間内に改めてお問い合わせください。',
    condition: '営業時間外の場合',
    isActive: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 4,
    name: 'サービス終了案内',
    category: 'guidance',
    content: 'ご利用いただきありがとうございました。\n今後ともよろしくお願いいたします。',
    condition: 'サービス利用終了時',
    isActive: true,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-22')
  },
  {
    id: 5,
    name: 'エラー発生時対応',
    category: 'error',
    content: 'システムエラーが発生しました。\n恐れ入りますが、しばらく時間をおいて再度お試しください。',
    condition: 'システムエラー発生時',
    isActive: false,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-25')
  }
])

// Table columns
const columns = [
  {
    key: "name",
    label: "テンプレート名",
    sortable: true,
  },
  {
    key: "content",
    label: "内容",
    sortable: false,
  },
  {
    key: "condition",
    label: "使用条件",
    sortable: false,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalTemplatesCount = computed(() => templates.value.length)

const paginatedTemplates = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...templates.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalTemplatesCount.value)
})

const templateForm = reactive({
  name: '',
  category: '',
  content: '',
  condition: ''
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getCategoryLabel = (category: string) => {
  const option = categoryOptions.find(opt => opt.value === category)
  return option ? option.label : category
}

const refreshTemplates = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'テンプレート一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}

const createTemplate = () => {
  editingTemplate.value = null
  Object.assign(templateForm, {
    name: '',
    category: '',
    content: '',
    condition: ''
  })
  showModal.value = true
}

const editTemplate = (template) => {
  editingTemplate.value = template
  Object.assign(templateForm, {
    name: template.name,
    category: template.category,
    content: template.content,
    condition: template.condition
  })
  showModal.value = true
}

const saveTemplate = async () => {
  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingTemplate.value) {
      // Update existing template
      Object.assign(editingTemplate.value, templateForm)
      editingTemplate.value.updatedAt = new Date()
    } else {
      // Create new template
      templates.value.push({
        id: Date.now(),
        ...templateForm,
        isActive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    closeModal()
    
    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `テンプレートを${editingTemplate.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'テンプレートの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingTemplate.value = null
}

const toggleTemplate = async (template) => {
  template.isActive = !template.isActive
  template.updatedAt = new Date()
  
  const toast = useToast()
  toast.add({
    title: template.isActive ? 'テンプレートを有効化しました' : 'テンプレートを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateTemplate = (template) => {
  const newTemplate = {
    ...template,
    id: Date.now(),
    name: `${template.name} (コピー)`,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  templates.value.push(newTemplate)
  
  const toast = useToast()
  toast.add({
    title: 'テンプレートを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteTemplate = (template) => {
  const index = templates.value.findIndex(t => t.id === template.id)
  if (index > -1) {
    templates.value.splice(index, 1)
    
    const toast = useToast()
    toast.add({
      title: 'テンプレートを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
