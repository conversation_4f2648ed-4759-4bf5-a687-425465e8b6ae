<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="チャットボットログ" />
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-xl font-semibold">ログ管理</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            チャットボットの動作ログを確認できます
          </p>
        </div>
        <div class="flex space-x-3">
          <UButton color="gray" variant="soft" icon="i-heroicons-arrow-path" @click="refreshLogs">
            更新
          </UButton>
          <UButton color="gray" variant="soft" icon="i-heroicons-arrow-down-tray" @click="exportLogs">
            エクスポート
          </UButton>
        </div>
      </div>

      <!-- Filters -->
      <UCard class="mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <UFormGroup label="期間">
            <USelect v-model="filters.period" :options="periodOptions" />
          </UFormGroup>
          <UFormGroup label="ログレベル">
            <USelect v-model="filters.level" :options="levelOptions" />
          </UFormGroup>
          <UFormGroup label="ユーザーID">
            <UInput v-model="filters.userId" placeholder="ユーザーIDで検索" />
          </UFormGroup>
          <UFormGroup label="キーワード">
            <UInput v-model="filters.keyword" placeholder="メッセージ内容で検索" />
          </UFormGroup>
        </div>
        <div class="mt-4 flex justify-end space-x-2">
          <UButton color="gray" variant="soft" @click="clearFilters">
            クリア
          </UButton>
          <UButton color="primary" @click="applyFilters">
            検索
          </UButton>
        </div>
      </UCard>

      <!-- Logs Table -->
      <UCard>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-200 dark:border-gray-700">
                <th class="text-left py-3 px-4 font-semibold text-sm">時刻</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">レベル</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">ユーザー</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">アクション</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">メッセージ</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">詳細</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="log in filteredLogs" 
                :key="log.id"
                class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <td class="py-3 px-4 text-sm">
                  {{ formatDateTime(log.timestamp) }}
                </td>
                <td class="py-3 px-4">
                  <UBadge 
                    :color="getLevelColor(log.level)"
                    variant="soft"
                    size="sm"
                  >
                    {{ log.level }}
                  </UBadge>
                </td>
                <td class="py-3 px-4 text-sm">
                  {{ log.userId || '-' }}
                </td>
                <td class="py-3 px-4 text-sm">
                  {{ log.action }}
                </td>
                <td class="py-3 px-4 text-sm max-w-xs truncate">
                  {{ log.message }}
                </td>
                <td class="py-3 px-4">
                  <UButton 
                    color="gray" 
                    variant="ghost" 
                    size="sm"
                    icon="i-heroicons-eye"
                    @click="viewLogDetail(log)"
                  >
                    詳細
                  </UButton>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- Empty State -->
          <div v-if="filteredLogs.length === 0" class="text-center py-12">
            <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              ログが見つかりません
            </h3>
            <p class="text-gray-500">
              検索条件を変更してください
            </p>
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
          <div class="text-sm text-gray-500">
            {{ filteredLogs.length }} 件のログ
          </div>
          <div class="flex space-x-2">
            <UButton 
              color="gray" 
              variant="soft" 
              size="sm"
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              前へ
            </UButton>
            <span class="px-3 py-1 text-sm">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <UButton 
              color="gray" 
              variant="soft" 
              size="sm"
              :disabled="currentPage === totalPages"
              @click="currentPage++"
            >
              次へ
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Log Detail Modal -->
    <UModal v-model="showDetailModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard v-if="selectedLog">
        <template #header>
          <h3 class="text-lg font-semibold">ログ詳細</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">時刻</label>
              <p class="text-sm">{{ formatDateTime(selectedLog.timestamp) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">レベル</label>
              <p class="text-sm">
                <UBadge 
                  :color="getLevelColor(selectedLog.level)"
                  variant="soft"
                  size="sm"
                >
                  {{ selectedLog.level }}
                </UBadge>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">ユーザーID</label>
              <p class="text-sm">{{ selectedLog.userId || '-' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">アクション</label>
              <p class="text-sm">{{ selectedLog.action }}</p>
            </div>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">メッセージ</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p class="text-sm whitespace-pre-wrap">{{ selectedLog.message }}</p>
            </div>
          </div>
          
          <div v-if="selectedLog.details">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">詳細情報</label>
            <div class="mt-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <pre class="text-xs overflow-x-auto">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton color="gray" variant="soft" @click="showDetailModal = false">
              閉じる
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-logs",
  middleware: ["permissions"],
});

const showDetailModal = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const itemsPerPage = 20

const periodOptions = [
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '過去7日', value: 'week' },
  { label: '過去30日', value: 'month' },
  { label: 'すべて', value: 'all' }
]

const levelOptions = [
  { label: 'すべて', value: 'all' },
  { label: 'INFO', value: 'info' },
  { label: 'WARN', value: 'warn' },
  { label: 'ERROR', value: 'error' },
  { label: 'DEBUG', value: 'debug' }
]

const filters = reactive({
  period: 'today',
  level: 'all',
  userId: '',
  keyword: ''
})

// Mock data
const logs = ref([
  {
    id: 1,
    timestamp: new Date('2024-01-20T10:30:00'),
    level: 'info',
    userId: 'user123',
    action: 'message_received',
    message: 'ユーザーからメッセージを受信: こんにちは',
    details: { messageId: 'msg_001', channel: 'line' }
  },
  {
    id: 2,
    timestamp: new Date('2024-01-20T10:30:05'),
    level: 'info',
    userId: 'user123',
    action: 'bot_response',
    message: 'チャットボットが応答: こんにちは！何かお困りのことはありませんか？',
    details: { scenarioId: 'scenario_001', templateId: 'template_001' }
  },
  {
    id: 3,
    timestamp: new Date('2024-01-20T10:25:00'),
    level: 'warn',
    userId: 'user456',
    action: 'scenario_not_found',
    message: '該当するシナリオが見つかりませんでした',
    details: { keyword: '不明なキーワード' }
  },
  {
    id: 4,
    timestamp: new Date('2024-01-20T10:20:00'),
    level: 'error',
    userId: null,
    action: 'system_error',
    message: 'チャットボットシステムエラーが発生しました',
    details: { error: 'Connection timeout', stack: 'Error stack trace...' }
  }
])

const filteredLogs = computed(() => {
  let result = logs.value

  // Apply filters
  if (filters.level !== 'all') {
    result = result.filter(log => log.level === filters.level)
  }

  if (filters.userId) {
    result = result.filter(log => 
      log.userId && log.userId.toLowerCase().includes(filters.userId.toLowerCase())
    )
  }

  if (filters.keyword) {
    result = result.filter(log => 
      log.message.toLowerCase().includes(filters.keyword.toLowerCase())
    )
  }

  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return result.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(logs.value.length / itemsPerPage)
})

const formatDateTime = (date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const getLevelColor = (level) => {
  switch (level) {
    case 'info': return 'blue'
    case 'warn': return 'yellow'
    case 'error': return 'red'
    case 'debug': return 'gray'
    default: return 'gray'
  }
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  showDetailModal.value = true
}

const refreshLogs = () => {
  // TODO: Implement API call to refresh logs
  const toast = useToast()
  toast.add({
    title: 'ログを更新しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const exportLogs = () => {
  // TODO: Implement log export functionality
  const toast = useToast()
  toast.add({
    title: 'ログのエクスポートを開始しました',
    description: 'ダウンロードが完了するまでお待ちください',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const clearFilters = () => {
  Object.assign(filters, {
    period: 'today',
    level: 'all',
    userId: '',
    keyword: ''
  })
  currentPage.value = 1
}

const applyFilters = () => {
  currentPage.value = 1
  // Filters are applied automatically through computed property
}
</script>
