<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="シナリオ管理" />
    <div class="px-6 pt-6">
      <BaseTable
        title="チャットボットシナリオ"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalScenariosCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="createScenario"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshScenarios"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedScenarios"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-text" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                シナリオがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-heroicons-document-text" class="text-blue-500" />
              <div>
                <div class="font-semibold">{{ row.name }}</div>
                <p class="text-xs text-gray-500">{{ row.triggerKeyword ? `キーワード: ${row.triggerKeyword}` : 'キーワードなし' }}</p>
              </div>
            </div>
          </template>
          <template #description-data="{ row }">
            <div class="max-w-xs">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ row.description }}
              </p>
            </div>
          </template>
          <template #steps-data="{ row }">
            <div class="text-center">
              <span class="text-lg font-semibold">{{ row.steps }}</span>
              <p class="text-xs text-gray-500">ステップ</p>
            </div>
          </template>
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="soft"
              size="sm"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="editScenario(row)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleScenario(row)"
              />
              <UButton
                icon="i-heroicons-document-duplicate"
                size="xs"
                color="gray"
                variant="soft"
                label="複製"
                @click="duplicateScenario(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingScenario ? 'シナリオ編集' : '新規シナリオ作成' }}
          </h3>
        </template>
        
        <div class="space-y-4">
          <UFormGroup label="シナリオ名" required>
            <UInput v-model="scenarioForm.name" placeholder="例: 初回問い合わせ対応" />
          </UFormGroup>
          <UFormGroup label="説明">
            <UTextarea 
              v-model="scenarioForm.description" 
              placeholder="このシナリオの説明を入力してください"
              :rows="3"
            />
          </UFormGroup>
          <UFormGroup label="トリガーキーワード">
            <UInput 
              v-model="scenarioForm.triggerKeyword" 
              placeholder="例: こんにちは, はじめまして"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton color="gray" variant="soft" @click="closeModal">
              キャンセル
            </UButton>
            <UButton color="primary" @click="saveScenario" :loading="saving">
              {{ editingScenario ? '更新' : '作成' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-scenario",
  middleware: ["permissions"],
});

const showModal = ref(false)
const saving = ref(false)
const loading = ref(false)
const editingScenario = ref(null)

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})

const scenarios = ref([
  {
    id: 1,
    name: '初回問い合わせ対応',
    description: '初めて問い合わせをするユーザー向けの基本的な対応フロー',
    steps: 5,
    isActive: true,
    triggerKeyword: 'こんにちは',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 2,
    name: 'FAQ自動応答',
    description: 'よくある質問に対する自動応答シナリオ',
    steps: 8,
    isActive: true,
    triggerKeyword: '質問',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 3,
    name: '営業時間外対応',
    description: '営業時間外のユーザー対応フロー',
    steps: 3,
    isActive: false,
    triggerKeyword: '',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 4,
    name: '製品紹介シナリオ',
    description: '製品の特徴や機能を紹介するシナリオ',
    steps: 12,
    isActive: true,
    triggerKeyword: '製品',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-22')
  },
  {
    id: 5,
    name: 'トラブルシューティング',
    description: '一般的な問題の解決手順を案内するシナリオ',
    steps: 15,
    isActive: false,
    triggerKeyword: 'トラブル',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-25')
  }
])

// Table columns
const columns = [
  {
    key: "name",
    label: "シナリオ名",
    sortable: true,
  },
  {
    key: "description",
    label: "説明",
    sortable: false,
  },
  {
    key: "steps",
    label: "ステップ数",
    sortable: true,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalScenariosCount = computed(() => scenarios.value.length)

const paginatedScenarios = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...scenarios.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalScenariosCount.value)
})

const scenarioForm = reactive({
  name: '',
  description: '',
  triggerKeyword: ''
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const refreshScenarios = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'シナリオ一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}

const createScenario = () => {
  editingScenario.value = null
  Object.assign(scenarioForm, {
    name: '',
    description: '',
    triggerKeyword: ''
  })
  showModal.value = true
}

const editScenario = (scenario) => {
  editingScenario.value = scenario
  Object.assign(scenarioForm, {
    name: scenario.name,
    description: scenario.description,
    triggerKeyword: scenario.triggerKeyword
  })
  showModal.value = true
}

const saveScenario = async () => {
  saving.value = true
  try {
    // TODO: Implement API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingScenario.value) {
      // Update existing scenario
      Object.assign(editingScenario.value, scenarioForm)
      editingScenario.value.updatedAt = new Date()
    } else {
      // Create new scenario
      scenarios.value.push({
        id: Date.now(),
        ...scenarioForm,
        steps: 1,
        isActive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    closeModal()
    
    const toast = useToast()
    toast.add({
      title: '保存完了',
      description: `シナリオを${editingScenario.value ? '更新' : '作成'}しました。`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'シナリオの保存に失敗しました。',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  editingScenario.value = null
}

const toggleScenario = async (scenario) => {
  scenario.isActive = !scenario.isActive
  scenario.updatedAt = new Date()
  
  const toast = useToast()
  toast.add({
    title: scenario.isActive ? 'シナリオを有効化しました' : 'シナリオを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateScenario = (scenario) => {
  const newScenario = {
    ...scenario,
    id: Date.now(),
    name: `${scenario.name} (コピー)`,
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  scenarios.value.push(newScenario)
  
  const toast = useToast()
  toast.add({
    title: 'シナリオを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const deleteScenario = (scenario) => {
  const index = scenarios.value.findIndex(s => s.id === scenario.id)
  if (index > -1) {
    scenarios.value.splice(index, 1)
    
    const toast = useToast()
    toast.add({
      title: 'シナリオを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}
</script>
