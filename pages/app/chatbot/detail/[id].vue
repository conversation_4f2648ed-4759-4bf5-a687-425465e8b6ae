<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader :title="`チャットボット詳細: ${chatbot?.name || 'Loading...'}`">
      <template #actions>
        <div class="flex space-x-3">
          <UButton 
            color="gray" 
            variant="soft" 
            icon="i-heroicons-arrow-left"
            @click="$router.back()"
          >
            戻る
          </UButton>
          <UButton 
            color="primary" 
            icon="i-heroicons-pencil"
            @click="editChatbot"
          >
            編集
          </UButton>
        </div>
      </template>
    </AppPageHeader>
    
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>

    <div v-else-if="chatbot" class="p-6">
      <!-- Basic Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <UCard>
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-blue-500" />
              <h3 class="text-lg font-semibold">基本情報</h3>
            </div>
          </template>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">チャットボット名</label>
              <p class="text-lg font-semibold mt-1">{{ chatbot.name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">ID</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 font-mono">{{ chatbot.id }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">説明</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ chatbot.description || '説明なし' }}
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">ステータス</label>
              <div class="mt-1">
                <UBadge 
                  :color="chatbot.isActive ? 'green' : 'gray'"
                  variant="soft"
                  size="sm"
                >
                  {{ chatbot.isActive ? '有効' : '無効' }}
                </UBadge>
              </div>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-cog-6-tooth" class="text-green-500" />
              <h3 class="text-lg font-semibold">設定情報</h3>
            </div>
          </template>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">自動応答</label>
              <div class="mt-1">
                <UBadge 
                  :color="chatbot.autoResponse ? 'blue' : 'gray'"
                  variant="soft"
                  size="sm"
                >
                  {{ chatbot.autoResponse ? '有効' : '無効' }}
                </UBadge>
              </div>
            </div>
            <div v-if="chatbot.autoResponse">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">応答待機時間</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.responseDelay }}秒</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">シナリオ数</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.scenarioCount }}個</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">テンプレート数</label>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.templateCount }}個</p>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Welcome Message -->
      <UCard class="mb-8">
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-chat-bubble-left-ellipsis" class="text-purple-500" />
            <h3 class="text-lg font-semibold">ウェルカムメッセージ</h3>
          </div>
        </template>
        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p class="whitespace-pre-wrap">{{ chatbot.welcomeMessage }}</p>
        </div>
      </UCard>

      <!-- Statistics -->
      <UCard class="mb-8">
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-chart-bar" class="text-orange-500" />
            <h3 class="text-lg font-semibold">統計情報</h3>
          </div>
        </template>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ stats.totalMessages }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">総メッセージ数</div>
          </div>
          <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ stats.successfulResponses }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">成功応答数</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ stats.averageResponseTime }}ms</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">平均応答時間</div>
          </div>
          <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ stats.activeUsers }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">アクティブユーザー</div>
          </div>
        </div>
      </UCard>

      <!-- Timestamps -->
      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-clock" class="text-gray-500" />
            <h3 class="text-lg font-semibold">作成・更新情報</h3>
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">作成日時</label>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ formatDateTime(chatbot.createdAt) }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">最終更新日時</label>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ formatDateTime(chatbot.updatedAt) }}</p>
          </div>
        </div>
      </UCard>

      <!-- Action Buttons -->
      <div class="mt-8 flex justify-end space-x-3">
        <UButton 
          :color="chatbot.isActive ? 'red' : 'green'"
          variant="soft"
          @click="toggleChatbot"
        >
          {{ chatbot.isActive ? '無効化' : '有効化' }}
        </UButton>
        <UButton color="gray" variant="soft" @click="duplicateChatbot">
          複製
        </UButton>
        <UButton color="red" variant="soft" @click="deleteChatbot">
          削除
        </UButton>
      </div>
    </div>

    <div v-else class="p-6">
      <div class="text-center py-12">
        <UIcon name="i-heroicons-exclamation-triangle" class="text-6xl text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
          チャットボットが見つかりません
        </h3>
        <p class="text-gray-500 mb-4">
          指定されたIDのチャットボットは存在しません
        </p>
        <UButton color="primary" @click="$router.push('/app/chatbot')">
          一覧に戻る
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const route = useRoute()
const router = useRouter()
const loading = ref(true)

// Mock chatbot data
const chatbots = [
  {
    id: 'bot_001',
    name: 'サポートボット',
    description: '一般的なサポート業務を担当するチャットボット',
    welcomeMessage: 'こんにちは！何かお困りのことはありませんか？\n\n以下のような内容でお手伝いできます：\n・製品に関する質問\n・技術サポート\n・一般的なお問い合わせ',
    isActive: true,
    autoResponse: true,
    responseDelay: 30,
    scenarioCount: 5,
    templateCount: 8,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'bot_002',
    name: 'FAQ自動応答ボット',
    description: 'よくある質問に自動で回答するチャットボット',
    welcomeMessage: 'よくある質問にお答えします。どのようなことでお困りですか？',
    isActive: true,
    autoResponse: false,
    responseDelay: 60,
    scenarioCount: 12,
    templateCount: 15,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'bot_003',
    name: '営業時間外対応ボット',
    description: '営業時間外の問い合わせに対応するチャットボット',
    welcomeMessage: '申し訳ございませんが、現在営業時間外です。',
    isActive: false,
    autoResponse: true,
    responseDelay: 10,
    scenarioCount: 3,
    templateCount: 5,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  }
]

// Mock statistics data
const stats = ref({
  totalMessages: 1247,
  successfulResponses: 1156,
  averageResponseTime: 850,
  activeUsers: 23
})

const chatbot = computed(() => {
  return chatbots.find(bot => bot.id === route.params.id)
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const editChatbot = () => {
  router.push(`/app/chatbot/edit/${route.params.id}`)
}

const toggleChatbot = () => {
  if (chatbot.value) {
    chatbot.value.isActive = !chatbot.value.isActive
    chatbot.value.updatedAt = new Date()
    
    const toast = useToast()
    toast.add({
      title: chatbot.value.isActive ? 'チャットボットを有効化しました' : 'チャットボットを無効化しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }
}

const duplicateChatbot = () => {
  const toast = useToast()
  toast.add({
    title: 'チャットボットを複製しました',
    description: '新しいチャットボットが作成されました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
  router.push('/app/chatbot')
}

const deleteChatbot = () => {
  const toast = useToast()
  toast.add({
    title: 'チャットボットを削除しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
  router.push('/app/chatbot')
}

// Simulate loading
onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>
