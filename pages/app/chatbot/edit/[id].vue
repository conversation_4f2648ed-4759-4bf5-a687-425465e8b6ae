<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader :title="`チャットボット編集: ${chatbot?.name || 'Loading...'}`">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>

    <div v-else-if="chatbot" class="p-6">
      <div class="max-w-4xl mx-auto">
        <form @submit.prevent="updateChatbot" class="space-y-8">
          <!-- Basic Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-blue-500" />
                <h3 class="text-lg font-semibold">基本情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボット名" required>
                <UInput 
                  v-model="form.name" 
                  placeholder="例: サポートボット"
                  :error="errors.name"
                />
              </UFormGroup>
              <UFormGroup label="説明">
                <UTextarea 
                  v-model="form.description"
                  placeholder="このチャットボットの用途や機能について説明してください"
                  :rows="3"
                />
              </UFormGroup>
              <UFormGroup label="ウェルカムメッセージ" required>
                <UTextarea 
                  v-model="form.welcomeMessage"
                  placeholder="こんにちは！何かお困りのことはありませんか？"
                  :rows="4"
                  :error="errors.welcomeMessage"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    ユーザーが最初にチャットボットと接触した際に表示されるメッセージです
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Settings -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-cog-6-tooth" class="text-green-500" />
                <h3 class="text-lg font-semibold">動作設定</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボットの有効化">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    チャットボット機能を有効にします
                  </span>
                  <UToggle
                    v-model="form.isActive"
                    on-icon="i-heroicons-check-20-solid"
                    off-icon="i-heroicons-x-mark-20-solid"
                  />
                </div>
              </UFormGroup>
              
              <UFormGroup label="自動応答機能">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    一定時間後に自動でメッセージを送信します
                  </span>
                  <UToggle
                    v-model="form.autoResponse"
                    on-icon="i-heroicons-check-20-solid"
                    off-icon="i-heroicons-x-mark-20-solid"
                  />
                </div>
              </UFormGroup>
              
              <UFormGroup 
                v-if="form.autoResponse"
                label="応答待機時間（秒）" 
                required
              >
                <UInput 
                  v-model="form.responseDelay" 
                  type="number"
                  min="1"
                  max="300"
                  placeholder="30"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    1秒から300秒（5分）まで設定可能です
                  </span>
                </template>
              </UFormGroup>
              
              <UFormGroup label="営業時間外メッセージ">
                <UTextarea 
                  v-model="form.offHoursMessage"
                  placeholder="申し訳ございませんが、現在営業時間外です。営業時間は平日9:00-18:00となっております。"
                  :rows="3"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    営業時間外にユーザーがアクセスした際に表示されるメッセージです
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Metadata -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-information-circle" class="text-gray-500" />
                <h3 class="text-lg font-semibold">メタデータ</h3>
              </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">チャットボットID</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 font-mono">{{ chatbot.id }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">作成日時</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ formatDateTime(chatbot.createdAt) }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">シナリオ数</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.scenarioCount }}個</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">テンプレート数</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.templateCount }}個</p>
              </div>
            </div>
          </UCard>

          <!-- Action Buttons -->
          <div class="flex justify-between">
            <UButton 
              color="red" 
              variant="soft"
              icon="i-heroicons-trash"
              @click="deleteChatbot"
              :disabled="saving"
            >
              削除
            </UButton>
            <div class="flex space-x-3">
              <UButton 
                color="gray" 
                variant="soft" 
                @click="$router.back()"
                :disabled="saving"
              >
                キャンセル
              </UButton>
              <UButton 
                color="primary" 
                type="submit"
                :loading="saving"
              >
                更新
              </UButton>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div v-else class="p-6">
      <div class="text-center py-12">
        <UIcon name="i-heroicons-exclamation-triangle" class="text-6xl text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
          チャットボットが見つかりません
        </h3>
        <p class="text-gray-500 mb-4">
          指定されたIDのチャットボットは存在しません
        </p>
        <UButton color="primary" @click="$router.push('/app/chatbot')">
          一覧に戻る
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const saving = ref(false)

// Mock chatbot data
const chatbots = [
  {
    id: 'bot_001',
    name: 'サポートボット',
    description: '一般的なサポート業務を担当するチャットボット',
    welcomeMessage: 'こんにちは！何かお困りのことはありませんか？\n\n以下のような内容でお手伝いできます：\n・製品に関する質問\n・技術サポート\n・一般的なお問い合わせ',
    isActive: true,
    autoResponse: true,
    responseDelay: 30,
    offHoursMessage: '申し訳ございませんが、現在営業時間外です。',
    scenarioCount: 5,
    templateCount: 8,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'bot_002',
    name: 'FAQ自動応答ボット',
    description: 'よくある質問に自動で回答するチャットボット',
    welcomeMessage: 'よくある質問にお答えします。どのようなことでお困りですか？',
    isActive: true,
    autoResponse: false,
    responseDelay: 60,
    offHoursMessage: '',
    scenarioCount: 12,
    templateCount: 15,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  }
]

const chatbot = computed(() => {
  return chatbots.find(bot => bot.id === route.params.id)
})

const form = reactive({
  name: '',
  description: '',
  welcomeMessage: '',
  isActive: false,
  autoResponse: false,
  responseDelay: 30,
  offHoursMessage: ''
})

const errors = reactive({
  name: '',
  welcomeMessage: ''
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const validateForm = () => {
  errors.name = ''
  errors.welcomeMessage = ''
  
  let isValid = true
  
  if (!form.name.trim()) {
    errors.name = 'チャットボット名は必須です'
    isValid = false
  }
  
  if (!form.welcomeMessage.trim()) {
    errors.welcomeMessage = 'ウェルカムメッセージは必須です'
    isValid = false
  }
  
  return isValid
}

const updateChatbot = async () => {
  if (!validateForm()) {
    return
  }
  
  saving.value = true
  
  try {
    // TODO: Implement API call to update chatbot
    console.log('Updating chatbot:', form)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Update local data
    if (chatbot.value) {
      Object.assign(chatbot.value, form)
      chatbot.value.updatedAt = new Date()
    }
    
    const toast = useToast()
    toast.add({
      title: 'チャットボットを更新しました',
      description: `${form.name}の設定が保存されました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの更新に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const deleteChatbot = async () => {
  const confirmed = confirm('このチャットボットを削除してもよろしいですか？この操作は取り消せません。')
  
  if (!confirmed) return
  
  try {
    // TODO: Implement API call to delete chatbot
    console.log('Deleting chatbot:', route.params.id)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const toast = useToast()
    toast.add({
      title: 'チャットボットを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
    router.push('/app/chatbot')
    
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの削除に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  }
}

// Load chatbot data and populate form
onMounted(() => {
  setTimeout(() => {
    if (chatbot.value) {
      Object.assign(form, {
        name: chatbot.value.name,
        description: chatbot.value.description,
        welcomeMessage: chatbot.value.welcomeMessage,
        isActive: chatbot.value.isActive,
        autoResponse: chatbot.value.autoResponse,
        responseDelay: chatbot.value.responseDelay,
        offHoursMessage: chatbot.value.offHoursMessage
      })
    }
    loading.value = false
  }, 500)
})
</script>
