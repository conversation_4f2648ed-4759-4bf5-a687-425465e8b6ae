# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Build and Deploy to S3 and Cloudfront development

on:
  push:
    branches: ["develop"]

jobs:
  build:
    runs-on: ubuntu-latest
    environment: 検証環境
    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node ${{ matrix.node-version }} and build app
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - uses: pnpm/action-setup@v2
        name: Install pnpm
        with:
          version: 8
          run_install: false
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
      - uses: actions/cache@v3
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      - name: Install dependencies
        run: pnpm install
      - name: Build
        run: |
          export NUXT_APP_VERSION=$(git rev-parse --short HEAD | cut -c1-6)
          export NUXT_APP_BUILD_DATE=$(date +'%Y%m%dT%H%M%S')
          pnpm run generate
        env:
            TZ: "Asia/Tokyo"
            NUXT_USE_MOCK_API: ${{ vars.NUXT_USE_MOCK_API }}
            NUXT_APP_MAJOR_VERSION: ${{ vars.NUXT_APP_MAJOR_VERSION }}
            NUXT_API_AUTHENTICATE_BASE_URL: ${{ vars.NUXT_API_AUTHENTICATE_BASE_URL }}
            NUXT_API_ADMIN_BASE_URL: ${{ vars.NUXT_API_ADMIN_BASE_URL }}
            NUXT_LINE_CHAT_LIFF_ID: ${{ vars.NUXT_LINE_CHAT_LIFF_ID }}
            NUXT_LINE_LOGIN_REDIRECT_URL: ${{ vars.NUXT_LINE_LOGIN_REDIRECT_URL }}
            NUXT_SOCKET_BASE_URL: ${{ vars.NUXT_SOCKET_BASE_URL }}
            NUXT_APP_VERSION: ${{ vars.NUXT_APP_VERSION }}
            NUXT_APP_BUILD_DATE: ${{ vars.NUXT_APP_BUILD_DATE }}
      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
            name: nuxt-app
            path: .output/public

  deploy:
    runs-on: ubuntu-latest
    environment: 検証環境
    needs: build
    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: nuxt-app
          path: dist
      - name: Display structure of downloaded files
        run: ls -R
        working-directory: dist

      - name: s3 sync to bucket
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read --follow-symlinks --delete
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          SOURCE_DIR: "dist"
