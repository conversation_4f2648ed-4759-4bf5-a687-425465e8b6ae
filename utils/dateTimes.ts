import { extend, unix, locale } from "dayjs";
import "dayjs/locale/ja";
import relativeTime from "dayjs/plugin/relativeTime";
import dayjs from "dayjs";
import UTC from "dayjs/plugin/utc";
import customParseFormat from "dayjs/plugin/customParseFormat";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(customParseFormat);
dayjs.extend(isBetween);
locale("ja");
extend(relativeTime);
extend(UTC);
export const fromNowByTimestamp = (timestamp: number) => {
  if (!timestamp) {
    return "";
  }
  return unix(timestamp).fromNow();
};

export const fromNow = (date: Date) => {
  if (!date) {
    return "";
  }
  return unix(date.getTime() / 1000).fromNow();
};

export const fromNowByHour = (date: Date) => {
  if (!date) {
    return "";
  }
  // check if date is over 24 hours ago, return date format instead of fromNow
  if (dayjs().diff(date, "hours") > 24) {
    return unix(date.getTime() / 1000).format("YYYY年MM月DD日 HH:mm");
  }
  return unix(date.getTime() / 1000).fromNow();
};

export const formatDate = (
  date: Date,
  formatString: string = "YYYY年MM月DD日",
) => {
  if (!date) {
    return "";
  }
  return unix(date.getTime() / 1000).format(formatString);
};

export const formatDateForMessage = (date: Date) => {
  if (!date) {
    return "";
  }

  // compare date with today
  const today = dayjs();
  const dateDayjs = dayjs.utc(date).local();
  if (today.isSame(dateDayjs, "day")) {
    return dateDayjs.format("HH:mm");
  } else if (today.isSame(dateDayjs, "year")) {
    return dateDayjs.format("M月D日 HH:mm");
  } else {
    return dateDayjs.format("YYYY年M月D日 HH:mm");
  }
};

export const fromNowByDatetime = (datetime: string | undefined): string => {
  if (!datetime) {
    return "";
  }
  return dayjs.utc(datetime).fromNow();
};

export const parseTimeToObject = (time: string, format: string = "HH:mm") => {
  const date = dayjs(time, format);
  return {
    hours: date.hour(),
    minutes: date.minute(),
  };
};

export const parseTimeToString = (
  time: { hours: number; minutes: number },
  format: string = "HH:mm",
) => {
  return dayjs()
    .set("hour", time.hours)
    .set("minute", time.minutes)
    .format(format);
};

export const parseYearMonthToString = (
  year: number,
  month: number,
  format: string = "YYYYMM",
) => {
  return dayjs()
    .set("year", year)
    .set("month", month - 1)
    .format(format);
};

export const isValidDate = (date: string, format: string = "YYYY-MM-DD") => {
  return dayjs(date, format).isValid();
};

export const nowDate = () => {
  return dayjs().format("YYYY-MM-DD");
};
    // hard code for hamamatsu-city -> need to implement it by api later
export const isChatAvailableTime = (slug: string) => {
  // 1. 毎週火・木曜日と一定期間（5/5～5/12、6/5～6/18、8/19～9/6、1/5～1/21
  // 2. 17～21時
  if (slug !== "hamamatsu-city") return true;

  const today = dayjs();
  const runtimeConfig = useRuntimeConfig();
  const { hamamatsuChatAvailable } = runtimeConfig.public;
  const { days, times, dates } = hamamatsuChatAvailable;
  const isAvaiableDay = days.includes(today.day());
  const isAvaiableTime = times.includes(today.hour());
  const isAvaiableDate = dates.some((date: any) => {
    return today.isBetween(date[0], date[1]);
  });
  return isAvaiableDay && isAvaiableTime && isAvaiableDate;
};
